import pytest
from tool_lib.base import Tool
from tool_lib.milling import <PERSON>Mill
from tool_lib.drilling import DrillBit
from tool_lib.tapping import Tap
from tool_lib.turning import (TurningTool, ExternalTurningTool, InternalTurningTool)

@ pytest.mark.parametrize("cls, kwargs, expected_str", [
    (Tool, {"name": "Generic", "diameter": 10, "length": 50}, "Tool: Generic (Ø10mm, L=50mm)"),
    (EndMill, {"name": "EM1", "diameter": 5, "length": 40, "flutes": 2, "mill_type": "flat"}, "Flat EndMill: EM1 (Ø5mm, 2 flutes)"),
    (DrillBit, {"name": "DB1", "diameter": 3, "length": 60, "point_angle": 118}, "Drill Bit: DB1 (Ø3mm, angle 118°)"),
    (Tap, {"name": "T1", "diameter": 8, "length": 80, "pitch": 1.25, "thread": "M8"}, "Tap: T1 (M8, pitch 1.25mm)"),
    (ExternalTurningTool, {"name": "ET1", "length": 100, "insert_type": "PVD", "approach_angle": 95, "max_cutting_depth": 2, "operation": "finishing"},
     "External Turning Tool (Finishing): ET1 (insert PVD, max depth 2mm)"),
    (InternalTurningTool, {"name": "IT1", "length": 120, "insert_type": "CVD", "approach_angle": 90, "min_bore_diameter": 10, "max_depth": 30},
     "Internal Turning Tool: IT1 (min bore Ø10mm, max depth 30mm)"),
])

def test_str_methods(cls, kwargs, expected_str):
    instance = cls(**kwargs)
    assert str(instance) == expected_str


def test_get_parameters_override():
    tool = InternalTurningTool("IT2", 130, "CVD", 92, 12, 35, code="X123")
    params = tool.get_parameters()
    assert "diameter" not in params or params["diameter"] is None
    assert params["name"] == "IT2"
    assert params["code"] == "X123"
    assert params["min_bore_diameter"] == 12
    assert params["max_depth"] == 35
