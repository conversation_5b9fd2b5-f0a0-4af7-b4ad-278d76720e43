"""
Modulo per la generazione di percorsi di lavorazione CAM.

Supporta:
- Generazione percorsi per diverse lavorazioni
- Calcolo automatico di percorsi ottimizzati
- Gestione di superfici matematiche
- Algoritmi di generazione percorsi puri

Utilizza solo matematica pura senza dipendenze da librerie 3D.
"""

from typing import List, Tuple, Dict, Optional, Any
import math

from operations import Operation, OperationType, RoughingOperation, ContouringOperation, EngravingOperation, MathematicalSurface
from coordinate_system import CoordinateSystem

# Import tool_lib con gestione errori
try:
    from tool_lib.tool_lib.base import Tool
except ImportError:
    # Classe placeholder se tool_lib non disponibile
    class Tool:
        def __init__(self, name, diameter=None, length=None, code=None):
            self.name = name
            self.diameter = diameter
            self.length = length
            self.code = code


class PathPoint:
    """Rappresenta un punto nel percorso di lavorazione."""

    def __init__(self, x: float, y: float, z: float, feed_rate: Optional[float] = None,
                 spindle_speed: Optional[int] = None, is_rapid: bool = False):
        """
        Inizializza un punto del percorso.

        Args:
            x, y, z: Coordinate del punto
            feed_rate: Velocità di avanzamento specifica per questo punto
            spindle_speed: Velocità mandrino specifica per questo punto
            is_rapid: Se True, movimento rapido (G0)
        """
        self.x = x
        self.y = y
        self.z = z
        self.feed_rate = feed_rate
        self.spindle_speed = spindle_speed
        self.is_rapid = is_rapid

    def to_tuple(self) -> Tuple[float, float, float]:
        """Restituisce le coordinate come tupla."""
        return (self.x, self.y, self.z)

    def distance_to(self, other: 'PathPoint') -> float:
        """Calcola la distanza da un altro punto."""
        return math.sqrt((self.x - other.x)**2 + (self.y - other.y)**2 + (self.z - other.z)**2)


class ToolPath:
    """Rappresenta un percorso completo per un utensile."""

    def __init__(self, operation: Operation, tool: Tool):
        """
        Inizializza un percorso utensile.

        Args:
            operation: Lavorazione associata
            tool: Utensile utilizzato
        """
        self.operation = operation
        self.tool = tool
        self.points: List[PathPoint] = []
        self.segments: List[List[PathPoint]] = []
        self.total_length = 0.0
        self.machining_time = 0.0

    def add_point(self, point: PathPoint):
        """Aggiunge un punto al percorso."""
        self.points.append(point)

    def add_segment(self, segment: List[PathPoint]):
        """Aggiunge un segmento al percorso."""
        self.segments.append(segment)
        self.points.extend(segment)

    def calculate_length(self) -> float:
        """Calcola la lunghezza totale del percorso."""
        total = 0.0
        for i in range(1, len(self.points)):
            total += self.points[i-1].distance_to(self.points[i])
        self.total_length = total
        return total

    def calculate_time(self) -> float:
        """Calcola il tempo di lavorazione stimato."""
        if not self.points:
            return 0.0

        total_time = 0.0
        for i in range(1, len(self.points)):
            distance = self.points[i-1].distance_to(self.points[i])
            feed_rate = self.points[i].feed_rate or self.operation.feed_rate
            if not self.points[i].is_rapid and feed_rate > 0:
                total_time += distance / feed_rate  # tempo in minuti

        self.machining_time = total_time
        return total_time

    def _calculate_properties(self):
        """Calcola lunghezza e tempo del percorso."""
        self.calculate_length()
        self.calculate_time()


class PathGenerator:
    """Generatore principale di percorsi CAM."""

    def __init__(self, coordinate_system: Optional[CoordinateSystem] = None):
        """
        Inizializza il generatore di percorsi.

        Args:
            coordinate_system: Sistema di coordinate da utilizzare
        """
        self.coordinate_system = coordinate_system
        self.tolerance = 0.01  # mm
        self.optimization_enabled = True

    def generate_path(self, operation: Operation) -> ToolPath:
        """
        Genera il percorso per una lavorazione.

        Args:
            operation: Lavorazione da eseguire

        Returns:
            Percorso generato
        """
        if not operation.tool:
            raise ValueError("Nessun utensile specificato per la lavorazione")

        if not operation.surfaces:
            raise ValueError("Nessuna superficie specificata per la lavorazione")

        # Valida la lavorazione
        is_valid, errors = operation.validate()
        if not is_valid:
            raise ValueError(f"Lavorazione non valida: {', '.join(errors)}")

        # Genera il percorso in base al tipo di lavorazione
        if operation.operation_type == OperationType.ROUGHING:
            return self._generate_roughing_path(operation)
        elif operation.operation_type == OperationType.CONTOURING:
            return self._generate_contouring_path(operation)
        elif operation.operation_type == OperationType.ENGRAVING:
            return self._generate_engraving_path(operation)
        else:
            raise ValueError(f"Tipo di lavorazione non supportato: {operation.operation_type}")

    def _generate_roughing_path(self, operation: RoughingOperation) -> ToolPath:
        """Genera percorso di sgrossatura."""
        tool_path = ToolPath(operation, operation.tool)

        for surface in operation.surfaces:
            # Ottieni i limiti della superficie
            bounds = self._get_surface_bounds(surface)
            if not bounds:
                continue

            # Genera pattern di sgrossatura
            if operation.roughing_strategy == "zigzag":
                segments = self._generate_zigzag_pattern(bounds, operation)
            elif operation.roughing_strategy == "spiral":
                segments = self._generate_spiral_pattern(bounds, operation)
            else:
                segments = self._generate_parallel_pattern(bounds, operation)

            # Proietta i punti sulla superficie
            for segment in segments:
                projected_segment = self._project_points_on_surface(segment, surface, operation)
                if projected_segment:
                    tool_path.add_segment(projected_segment)

        return tool_path

    def _generate_contouring_path(self, operation: ContouringOperation) -> ToolPath:
        """Genera percorso di contornatura."""
        tool_path = ToolPath(operation, operation.tool)

        for surface in operation.surfaces:
            # Genera punti lungo il perimetro della superficie
            perimeter_points = self._generate_surface_perimeter(surface, operation.tolerance)

            if perimeter_points:
                # Aggiungi lead-in e lead-out
                if operation.lead_in_distance > 0:
                    lead_in = self._generate_lead_in(perimeter_points[0], operation.lead_in_distance)
                    perimeter_points = lead_in + perimeter_points

                if operation.lead_out_distance > 0:
                    lead_out = self._generate_lead_out(perimeter_points[-1], operation.lead_out_distance)
                    perimeter_points.extend(lead_out)

                # Ripeti per il numero di passate di finitura
                for pass_num in range(operation.finish_passes + operation.spring_passes):
                    is_spring_pass = pass_num >= operation.finish_passes
                    segment_points = []

                    for point in perimeter_points:
                        path_point = PathPoint(
                            point[0], point[1], point[2],
                            feed_rate=0 if is_spring_pass else operation.feed_rate
                        )
                        segment_points.append(path_point)

                    tool_path.add_segment(segment_points)

        return tool_path

    def _generate_engraving_path(self, operation: EngravingOperation) -> ToolPath:
        """Genera percorso di incisione."""
        tool_path = ToolPath(operation, operation.tool)

        if operation.text_content:
            # Genera percorso per testo
            text_paths = self._generate_text_paths(operation)
            for path in text_paths:
                tool_path.add_segment(path)
        else:
            # Genera percorso per superfici selezionate
            for surface in operation.surfaces:
                surface_path = self._generate_surface_engraving(surface, operation)
                if surface_path:
                    tool_path.add_segment(surface_path)

        return tool_path

    def _get_surface_bounds(self, surface: MathematicalSurface) -> Optional[Tuple[float, float, float, float]]:
        """Calcola i limiti di una superficie (xmin, ymin, xmax, ymax)."""
        bbox = surface.get_bounding_box()
        min_point, max_point = bbox
        return (min_point[0], min_point[1], max_point[0], max_point[1])

    def _generate_surface_perimeter(self, surface: MathematicalSurface, tolerance: float) -> List[Tuple[float, float, float]]:
        """Genera punti lungo il perimetro di una superficie."""
        bbox = surface.get_bounding_box()
        min_point, max_point = bbox
        xmin, ymin, zmin = min_point
        xmax, ymax, zmax = max_point

        # Usa la Z media per il perimetro
        z_avg = (zmin + zmax) / 2

        # Genera punti lungo il perimetro rettangolare
        points = []

        # Numero di punti per lato basato sulla tolleranza
        width = xmax - xmin
        height = ymax - ymin
        num_points_x = max(2, int(width / tolerance))
        num_points_y = max(2, int(height / tolerance))

        # Lato inferiore (da sinistra a destra)
        for i in range(num_points_x):
            x = xmin + (i / (num_points_x - 1)) * width
            points.append((x, ymin, z_avg))

        # Lato destro (dal basso verso l'alto, escludendo l'angolo)
        for i in range(1, num_points_y):
            y = ymin + (i / (num_points_y - 1)) * height
            points.append((xmax, y, z_avg))

        # Lato superiore (da destra a sinistra, escludendo l'angolo)
        for i in range(1, num_points_x):
            x = xmax - (i / (num_points_x - 1)) * width
            points.append((x, ymax, z_avg))

        # Lato sinistro (dall'alto verso il basso, escludendo gli angoli)
        for i in range(1, num_points_y - 1):
            y = ymax - (i / (num_points_y - 1)) * height
            points.append((xmin, y, z_avg))

        return points

    def _generate_zigzag_pattern(self, bounds: Tuple[float, float, float, float],
                                operation: RoughingOperation) -> List[List[Tuple[float, float, float]]]:
        """Genera pattern a zigzag per sgrossatura."""
        xmin, ymin, xmax, ymax = bounds
        step_over = operation.step_over

        segments = []
        y = ymin
        direction = 1

        while y <= ymax:
            if direction > 0:
                segment = [(xmin, y, 0), (xmax, y, 0)]
            else:
                segment = [(xmax, y, 0), (xmin, y, 0)]

            segments.append(segment)
            y += step_over
            direction *= -1

        return segments

    def _generate_spiral_pattern(self, bounds: Tuple[float, float, float, float],
                                operation: RoughingOperation) -> List[List[Tuple[float, float, float]]]:
        """Genera pattern a spirale per sgrossatura."""
        # Implementazione semplificata
        return self._generate_parallel_pattern(bounds, operation)

    def _generate_parallel_pattern(self, bounds: Tuple[float, float, float, float],
                                  operation: RoughingOperation) -> List[List[Tuple[float, float, float]]]:
        """Genera pattern parallelo per sgrossatura."""
        xmin, ymin, xmax, ymax = bounds
        step_over = operation.step_over

        segments = []
        y = ymin

        while y <= ymax:
            segment = [(xmin, y, 0), (xmax, y, 0)]
            segments.append(segment)
            y += step_over

        return segments

    def _project_points_on_surface(self, points: List[Tuple[float, float, float]],
                                  surface: MathematicalSurface, operation: Operation) -> List[PathPoint]:
        """Proietta punti su una superficie matematica."""
        projected_points = []

        # Ottieni informazioni sulla superficie
        bbox = surface.get_bounding_box()
        min_point, max_point = bbox

        # Calcola la Z di lavorazione
        if surface.surface_type == 'plane':
            # Per superfici piane, usa la Z del centro
            work_z = surface.parameters.get('center', (0, 0, 0))[2]
        else:
            # Per altre superfici, usa la Z media del bounding box
            work_z = (min_point[2] + max_point[2]) / 2

        # Sottrai la profondità di lavorazione
        z_offset = operation.step_down if hasattr(operation, 'step_down') else 0.1
        work_z -= z_offset

        for point in points:
            x, y, z = point

            # Verifica se il punto è dentro i limiti della superficie
            if (min_point[0] <= x <= max_point[0] and
                min_point[1] <= y <= max_point[1]):

                path_point = PathPoint(x, y, work_z, feed_rate=operation.feed_rate)
                projected_points.append(path_point)

        return projected_points

    def _generate_lead_in(self, start_point: Tuple[float, float, float],
                         distance: float) -> List[Tuple[float, float, float]]:
        """Genera punti di entrata graduale."""
        # Implementazione semplificata: movimento lineare
        x, y, z = start_point
        return [(x - distance, y, z + distance), start_point]

    def _generate_lead_out(self, end_point: Tuple[float, float, float],
                          distance: float) -> List[Tuple[float, float, float]]:
        """Genera punti di uscita graduale."""
        # Implementazione semplificata: movimento lineare
        x, y, z = end_point
        return [end_point, (x + distance, y, z + distance)]

    def _generate_text_paths(self, operation: EngravingOperation) -> List[List[PathPoint]]:
        """Genera percorsi per incisione di testo."""
        # Placeholder - integrazione con font processor
        paths = []

        # TODO: Integrare con il font processor esistente
        # per ora restituisce un percorso semplice
        if operation.text_content:
            simple_path = [
                PathPoint(0, 0, -operation.engraving_depth, feed_rate=operation.feed_rate),
                PathPoint(operation.font_size, 0, -operation.engraving_depth, feed_rate=operation.feed_rate)
            ]
            paths.append(simple_path)

        return paths

    def _generate_surface_engraving(self, surface: MathematicalSurface,
                                   operation: EngravingOperation) -> Optional[List[PathPoint]]:
        """Genera percorso di incisione per una superficie."""
        # Implementazione semplificata per incisione su superficie
        bbox = surface.get_bounding_box()
        min_point, max_point = bbox

        xmin, ymin = min_point[0], min_point[1]
        xmax, ymax = max_point[0], max_point[1]
        z_work = (min_point[2] + max_point[2]) / 2 - operation.engraving_depth

        # Genera un pattern di incisione semplice (linee parallele)
        points = []

        # Linee orizzontali
        line_spacing = getattr(operation, 'line_spacing', 1.0)
        num_lines = max(2, int((ymax - ymin) / line_spacing))

        for i in range(num_lines):
            y = ymin + (i / (num_lines - 1)) * (ymax - ymin)
            points.append(PathPoint(xmin, y, z_work, feed_rate=operation.feed_rate))
            points.append(PathPoint(xmax, y, z_work, feed_rate=operation.feed_rate))

        return points if points else None
