from .base import Tool

class Tap(Tool):
    def __init__(self, name: str, diameter: float, length: float,
                 pitch: float, thread: str, code: str = None):
        super().__init__(name, diameter, length, code)
        self.pitch = pitch  # in mm
        self.thread = thread  # e.g., "M8", "M10"

    def __str__(self):
        return f"Tap: {self.name} ({self.thread}, pitch {self.pitch}mm)"
