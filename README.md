# Libreria CAM - Computer Aided Manufacturing

Una libreria completa per la generazione di percorsi di lavorazione CNC con integrazione OpenCASCADE (OCC) per la gestione di geometrie 3D complesse.

## Caratteristiche Principali

### 🔧 Gestione Grezzi (Blank/Stock)
- **Grezzi Cilindrici**: Perfetti per lavorazioni di tornitura e fresatura 4/5 assi
- **<PERSON><PERSON><PERSON>**: Per lavorazioni di fresatura tradizionale
- **Grezzi Basati su Modello**: Derivati automaticamente da geometrie 3D esistenti
- **Offset Personalizzabili**: Controllo preciso dei sovrametalli

### 📐 Sistema di Coordinate Avanzato
- **Posizionamento Origine**: Controllo completo della posizione dell'origine
- **Orientamento Assi**: Configurazione flessibile della direzione degli assi
- **Assi Rotativi**: Supporto per assi A, B, C per lavorazioni multi-asse
- **Work Offset**: Gestione automatica di G54, G55, etc.

### ⚙️ Lavorazioni Supportate
- **Sgrossatura**: Rimozione rapida del materiale con strategie ottimizzate
- **Contornatura**: Finitura di precisione con controllo lead-in/lead-out
- **Incisione**: Testi e geometrie con supporto font TrueType
- **Foratura**: Cicli di foratura con controllo peck e dwell
- **Maschiatura**: Lavorazioni di filettatura

### 🎯 Ottimizzazione Percorsi
- **Conversione Archi**: Trasforma linee in archi per movimenti fluidi
- **Riduzione Punti**: Elimina punti ridondanti mantenendo la precisione
- **Riordinamento**: Ottimizza la sequenza per minimizzare i tempi morti
- **Controllo Tolleranze**: Precisione configurabile per ogni applicazione

## Struttura della Libreria

```
lib/CAM/
├── __init__.py              # Esportazioni principali
├── blank.py                 # Gestione grezzi
├── coordinate_system.py     # Sistemi di coordinate
├── operations.py            # Lavorazioni CAM
├── path_generator.py        # Generazione percorsi
├── optimization.py          # Ottimizzazione percorsi
├── setup.py                 # Setup completo CAM
├── gcode_generator.py       # Generazione G-code
├── example_usage.py         # Esempi di utilizzo
├── README.md               # Questa documentazione
└── tool_lib/               # Libreria utensili
    └── tool_lib/
        ├── base.py         # Classe base utensili
        ├── milling.py      # Utensili fresatura
        ├── drilling.py     # Utensili foratura
        ├── tapping.py      # Utensili maschiatura
        └── turning.py      # Utensili tornitura
```

## Utilizzo Base

### 1. Creazione Setup CAM

```python
from lib.CAM import CAMSetup, CylindricalBlank, CoordinateSystem
from lib.CAM.operations import OperationType
from lib.CAM.tool_lib.tool_lib.milling import EndMill

# Crea setup
setup = CAMSetup("Mia_Lavorazione")

# Configura grezzo
blank = CylindricalBlank("Cilindro_Alu", diameter=30.0, height=50.0)
setup.set_blank(blank)

# Sistema di coordinate
coord_system = CoordinateSystem("Standard")
coord_system.set_origin(0, 0, 0)
setup.set_coordinate_system(coord_system)
```

### 2. Aggiunta Lavorazioni

```python
# Crea utensile
tool = EndMill("Fresa_6mm", diameter=6.0, length=50.0, flutes=3, mill_type="flat")

# Aggiungi sgrossatura
roughing = setup.create_operation(
    operation_type=OperationType.ROUGHING,
    name="Sgrossatura",
    tool=tool
)

# Configura parametri
roughing.set_roughing_parameters(stock_to_leave=0.5, max_depth=2.0)
roughing.set_speeds_and_feeds(spindle_speed=2000, feed_rate=300)
```

### 3. Generazione Percorsi

```python
# Valida setup
is_valid, errors = setup.validate_setup()
if is_valid:
    # Genera percorsi ottimizzati
    tool_paths = setup.generate_all_paths(enable_optimization=True)
    
    # Esporta G-code
    setup.export_gcode("output.nc")
    
    # Mostra informazioni
    info = setup.get_setup_info()
    print(f"Tempo stimato: {info['total_machining_time']:.2f} minuti")
```

## Esempi Avanzati

### Incisione su Cilindro

```python
from lib.CAM import CylindricalBlank, create_standard_coordinate_systems

# Grezzo cilindrico
blank = CylindricalBlank("Cilindro", diameter=25.0, height=40.0)

# Sistema 4 assi
coord_systems = create_standard_coordinate_systems()
setup.set_coordinate_system(coord_systems['Milling_4Axis'])

# Incisione
engraving = setup.create_operation(OperationType.ENGRAVING, "Testo", tool)
engraving.set_engraving_parameters(depth=0.2, text="CAM LIB", font_size=8.0)
```

### Ottimizzazione Avanzata

```python
from lib.CAM.optimization import OptimizationSettings

# Configura ottimizzazione
setup.optimization_settings.enable_arc_fitting = True
setup.optimization_settings.chord_tolerance = 0.01
setup.optimization_settings.enable_point_reduction = True

# Genera con ottimizzazione
tool_paths = setup.generate_all_paths(enable_optimization=True)
```

### Creazione Automatica Grezzo

```python
from lib.CAM.blank import create_blank_from_model

# Da modello 3D esistente
blank = create_blank_from_model(
    model_shape=my_3d_model,
    blank_type="cylindrical",  # o "rectangular", "model_based"
    offset=5.0,
    material="Aluminum"
)
```

## Integrazione con OCC

La libreria utilizza OpenCASCADE per:
- **Analisi Geometrica**: Calcolo di bounding box, volumi, superfici
- **Proiezione Punti**: Proiezione di percorsi su superfici complesse
- **Estrazione Facce**: Selezione automatica di facce da lavorare
- **Discretizzazione**: Conversione di curve in punti per percorsi

## Parametri Raccomandati

La libreria include un database di parametri raccomandati per diversi materiali:

```python
from lib.CAM.operations import get_recommended_parameters

params = get_recommended_parameters(
    operation_type=OperationType.ROUGHING,
    material="Aluminum",
    tool_diameter=6.0
)
# Restituisce: {"spindle_speed": 2000, "feed_rate": 200}
```

## Materiali Supportati
- **Alluminio**: Parametri ottimizzati per leghe di alluminio
- **Acciaio**: Configurazioni per acciai al carbonio e inox
- **Plastica**: Impostazioni per materiali plastici
- **Legno**: Parametri per lavorazioni del legno

## Validazione e Sicurezza

Ogni componente include validazione automatica:
- **Setup**: Verifica completezza configurazione
- **Lavorazioni**: Controllo parametri e utensili
- **Percorsi**: Validazione geometrica e tolleranze
- **G-code**: Controllo sintassi e sicurezza

## Estensibilità

La libreria è progettata per essere facilmente estensibile:
- **Nuove Lavorazioni**: Eredita da `Operation`
- **Nuovi Utensili**: Eredita da `Tool`
- **Nuovi Grezzi**: Eredita da `Blank`
- **Ottimizzazioni Custom**: Modifica `OptimizationSettings`

## Dipendenze

- **OpenCASCADE**: Gestione geometrie 3D
- **NumPy**: Calcoli numerici
- **FontTools**: Elaborazione font per incisioni
- **SVGPathTools**: Conversione percorsi SVG

## Licenza

Questa libreria è parte del progetto CAM e segue la stessa licenza del progetto principale.

## Contributi

Per contribuire alla libreria:
1. Segui le convenzioni di codice esistenti
2. Aggiungi test per nuove funzionalità
3. Documenta le API pubbliche
4. Mantieni la compatibilità con le versioni esistenti
