class Tool:
    def __init__(self, name: str, diameter: float, length: float, code: str = None):
        self.name = name
        self.diameter = diameter  # in mm
        self.length = length      # in mm
        self.code = code

    def __str__(self):
        return f"Tool: {self.name} (Ø{self.diameter}mm, L={self.length}mm)"

    def get_parameters(self) -> dict:
        return {
            "name": self.name,
            "diameter": self.diameter,
            "length": self.length,
            "code": self.code,
        }