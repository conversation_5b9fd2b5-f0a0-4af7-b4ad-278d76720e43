from .base import Tool

class TurningTool(Tool):
    def __init__(self, name: str, length: float, insert_type: str,
                 approach_angle: float, code: str = None):
        super().__init__(name, diameter=None, length=length, code=code)
        self.insert_type = insert_type
        self.approach_angle = approach_angle  # in degrees

    def __str__(self):
        return f"Turning Tool: {self.name} (insert {self.insert_type}, angle {self.approach_angle}°)"

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params.pop("diameter", None)
        params.update({
            "insert_type": self.insert_type,
            "approach_angle": self.approach_angle,
        })
        return params

class ExternalTurningTool(TurningTool):
    def __init__(self, name: str, length: float, insert_type: str,
                 approach_angle: float, max_cutting_depth: float,
                 operation: str, code: str = None):
        super().__init__(name, length, insert_type, approach_angle, code)
        self.max_cutting_depth = max_cutting_depth  # in mm
        self.operation = operation  # e.g., "roughing", "finishing", "cut-off", "threading"

    def __str__(self):
        return (
            f"External Turning Tool ({self.operation.title()}): {self.name} "
            f"(insert {self.insert_type}, max depth {self.max_cutting_depth}mm)"
        )

class InternalTurningTool(TurningTool):
    def __init__(self, name: str, length: float, insert_type: str,
                 approach_angle: float, min_bore_diameter: float,
                 max_depth: float, code: str = None):
        super().__init__(name, length, insert_type, approach_angle, code)
        self.min_bore_diameter = min_bore_diameter  # in mm
        self.max_depth = max_depth  # in mm

    def __str__(self):
        return (
            f"Internal Turning Tool: {self.name} "
            f"(min bore Ø{self.min_bore_diameter}mm, max depth {self.max_depth}mm)"
        )

    def get_parameters(self) -> dict:
        params = super().get_parameters()
        params.update({
            "min_bore_diameter": self.min_bore_diameter,
            "max_depth": self.max_depth,
        })
        return params