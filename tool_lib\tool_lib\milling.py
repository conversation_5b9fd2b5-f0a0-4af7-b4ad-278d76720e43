from .base import Tool

class EndMill(Tool):
    def __init__(self, name: str, diameter: float, length: float,
                 flutes: int, mill_type: str, code: str = None):
        super().__init__(name, diameter, length, code)
        self.flutes = flutes
        self.mill_type = mill_type  # e.g., "flat", "ball", "torus"

    def __str__(self):
        return f"{self.mill_type.title()} EndMill: {self.name} (Ø{self.diameter}mm, {self.flutes} flutes)"
