"""
Modulo per la generazione del G-code per macchine CNC.
"""

from typing import List


class GCodeGenerator:
    def __init__(self, diameter: float, depth: float, feedrate: float, 
                num_passes: int = 1, safe_height: float = 10.0):
        """
        Inizializza il generatore di G-code.
        
        Args:
            diameter: Diametro del cilindro in mm
            depth: Profondità di incisione in mm
            feedrate: Velocità di avanzamento in mm/min
            num_passes: Numero di passate
            safe_height: Altezza di sicurezza in mm (aumentata a 10.0 mm di default)
        """
        self.diameter = diameter
        self.radius = diameter / 2
        self.depth = depth
        self.feedrate = feedrate
        self.num_passes = num_passes
        self.safe_height = safe_height
        self.safety_z = self.radius + safe_height
    
    def generate(self, cylindrical_paths: List, safety_margin: float = 5.0) -> List[str]:
        """
        Genera il G-code per l'incisione su cilindro.
        
        Args:
            cylindrical_paths: Lista di percorsi in coordinate cilindriche
            safety_margin: Margine di sicurezza aggiuntivo sopra il testo in mm (aumentato a 5.0 mm)
            
        Returns:
            Lista di stringhe contenenti il G-code
        """
        gcode = [
            "G21 ; Unità in mm",
            "G90 ; Posizionamento assoluto",
            f"F{self.feedrate} ; Velocità di avanzamento",
            "G54 ;"
        ]
        
        # Trova il punto iniziale (sopra la prima lettera)
        # Cerca il punto più a sinistra (minimo theta) e la sua altezza
        min_theta = float('inf')
        corresponding_height = 0
        
        for letter_paths in cylindrical_paths:
            for path in letter_paths:
                if not path:
                    continue
                for theta, height in path:
                    if theta < min_theta:
                        min_theta = theta
                        corresponding_height = height
        
        # Se non è stato trovato nessun punto (caso improbabile ma possibile)
        if min_theta == float('inf'):
            min_theta = 0
            corresponding_height = 0
        
        # Aggiungi il posizionamento iniziale sopra il testo con maggiore sicurezza
        final_safety_z = self.safety_z + safety_margin
        gcode.append(f"G0 Z{final_safety_z:.3f} A{-min_theta:.3f} X{corresponding_height:.3f} ; Posizionamento iniziale sopra il testo")
        
        depth_per_pass = self.depth / self.num_passes
        
        for pass_num in range(self.num_passes):
            current_depth = self.radius - depth_per_pass * (pass_num + 1)
            
            # Per ogni lettera
            for letter_idx, letter_paths in enumerate(cylindrical_paths):
                gcode.append(f"; Lettera {letter_idx+1}")
                
                last_point = None  # Inizializza l'ultimo punto come None
                
                # Per ogni contorno/tratto della lettera
                for contour_idx, path in enumerate(letter_paths):
                    if not path or len(path) < 2:
                        continue
                    
                    gcode.append(f"; Segmento {contour_idx+1}")
                    
                    # Ottieni il punto iniziale del segmento corrente
                    current_start = path[0]
                    
                    # Verifica se questo segmento è continuo con l'ultimo
                    is_continuous = (last_point is not None and 
                                    abs(last_point[0] - current_start[0]) < 0.001 and 
                                    abs(last_point[1] - current_start[1]) < 0.001)
                    
                    if not is_continuous:
                        # Se non è continuo, solleva e riposiziona
                        theta_start, height_start = current_start
                        gcode.append(f"G0 Z{self.safety_z:.3f} ; Sollevamento di sicurezza")
                        # Inverti il segno di theta_start
                        gcode.append(f"G0 X{height_start:.3f} A{-theta_start:.3f} ; Posizionamento")
                        gcode.append(f"G1 Z{current_depth:.3f} ; Discesa alla profondità di incisione")
                    
                    # Incide tutti i punti del contorno attuale
                    # Se è continuo, inizia dal secondo punto per evitare ripetizioni
                    start_idx = 1 if is_continuous else 0
                    for i in range(start_idx, len(path)):
                        theta, height = path[i]
                        # Inverti il segno di theta
                        gcode.append(f"G1 X{height:.3f} A{-theta:.3f}")
                    
                    # Aggiorna l'ultimo punto
                    last_point = path[-1]
                
                # Solleva sempre l'utensile dopo ogni lettera completata
                gcode.append(f"G1 Z{self.safety_z:.3f} ; Sollevamento di sicurezza")
        
        # Ritorna alla posizione iniziale sopra il testo con maggiore margine di sicurezza
        gcode.append(f"G0 Z{final_safety_z:.3f} A{-min_theta:.3f} X{corresponding_height:.3f} ; Ritorno alla posizione iniziale")
        
        return gcode
    
    def save_to_file(self, gcode: List[str], file_path: str) -> bool:
        """
        Salva il G-code su file.
        
        Args:
            gcode: Lista di stringhe contenenti il G-code
            file_path: Percorso del file di output
            
        Returns:
            True se il salvataggio è avvenuto con successo, False altrimenti
        """
        try:
            with open(file_path, "w") as f:
                f.write("\n".join(gcode))
            return True
        except Exception:
            return False