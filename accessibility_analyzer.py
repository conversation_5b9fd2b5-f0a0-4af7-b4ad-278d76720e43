"""
Modulo per l'analisi dell'accessibilità delle lavorazioni CAM.

Questo modulo fornisce funzionalità per:
- Analizzare l'accessibilità delle superfici durante le lavorazioni
- Generare mappe di accessibilità colorate
- Simulare il raggiungimento dell'utensile
- Identificare zone non lavorabili (sottosquadri, interferenze)
"""

import math
import numpy as np
from typing import List, Tuple, Dict, Any, Optional
from enum import Enum

# Import delle classi CAM
from blank import GeometricShape
from operations import MathematicalSurface, Operation

# Import PyVista per visualizzazione
try:
    import pyvista as pv
    PYVISTA_AVAILABLE = True
except ImportError:
    PYVISTA_AVAILABLE = False


class AccessibilityStatus(Enum):
    """Stati di accessibilità per i punti."""
    ACCESSIBLE = "accessible"           # Punto raggiungibile
    NOT_ACCESSIBLE = "not_accessible"   # Punto non raggiungibile
    PARTIAL = "partial"                 # Punto parzialmente raggiungibile
    COLLISION = "collision"             # Collisione con geometria
    OUT_OF_REACH = "out_of_reach"      # Fuori portata utensile


class AccessibilityPoint:
    """Rappresenta un punto con informazioni di accessibilità."""

    def __init__(self, position: Tuple[float, float, float],
                 normal: Tuple[float, float, float],
                 status: AccessibilityStatus,
                 distance_to_surface: float = 0.0,
                 tool_angle: float = 0.0):
        """
        Inizializza un punto di accessibilità.

        Args:
            position: Posizione del punto (x, y, z)
            normal: Normale alla superficie nel punto
            status: Stato di accessibilità
            distance_to_surface: Distanza dalla superficie target
            tool_angle: Angolo dell'utensile rispetto alla normale
        """
        self.position = position
        self.normal = normal
        self.status = status
        self.distance_to_surface = distance_to_surface
        self.tool_angle = tool_angle


class AccessibilityAnalyzer:
    """Analizzatore di accessibilità per lavorazioni CAM."""

    def __init__(self, resolution: int = 50):
        """
        Inizializza l'analizzatore.

        Args:
            resolution: Risoluzione della griglia di punti (punti per lato)
        """
        self.resolution = resolution
        self.points: List[AccessibilityPoint] = []
        self.mesh_data = None

    def analyze_surface_accessibility(self,
                                    operation: Operation,
                                    target_geometry: GeometricShape,
                                    approach_direction: Tuple[float, float, float] = (0, 0, -1),
                                    tool_length: float = 50.0,
                                    safety_distance: float = 2.0) -> List[AccessibilityPoint]:
        """
        Analizza l'accessibilità di una superficie per una data operazione.

        Args:
            operation: Operazione CAM da analizzare
            target_geometry: Geometria del pezzo target
            approach_direction: Direzione di approccio dell'utensile
            tool_length: Lunghezza dell'utensile
            safety_distance: Distanza di sicurezza

        Returns:
            Lista di punti con informazioni di accessibilità
        """
        print(f"Analisi accessibilità per operazione: {operation.name}")
        print(f"Risoluzione griglia: {self.resolution}x{self.resolution}")

        if not operation.surfaces:
            print("⚠️  Nessuna superficie definita per l'operazione")
            return []

        # Analizza ogni superficie dell'operazione
        all_points = []
        for i, surface in enumerate(operation.surfaces):
            print(f"  Analizzando superficie {i+1}/{len(operation.surfaces)}")
            surface_points = self._analyze_single_surface(
                surface, target_geometry, approach_direction,
                tool_length, safety_distance, operation.tool
            )
            all_points.extend(surface_points)

        self.points = all_points
        print(f"  Punti analizzati: {len(all_points)}")

        return all_points

    def _analyze_single_surface(self,
                               surface: MathematicalSurface,
                               target_geometry: GeometricShape,
                               approach_direction: Tuple[float, float, float],
                               tool_length: float,
                               safety_distance: float,
                               tool) -> List[AccessibilityPoint]:
        """Analizza una singola superficie."""
        points = []

        # Genera griglia di punti sulla superficie
        grid_points = self._generate_surface_grid(surface)

        for grid_point in grid_points:
            # Calcola normale alla superficie nel punto
            normal = self._calculate_surface_normal(surface, grid_point)

            # Verifica accessibilità
            accessibility = self._check_point_accessibility(
                grid_point, normal, target_geometry, approach_direction,
                tool_length, safety_distance, tool
            )

            points.append(accessibility)

        return points

    def _generate_surface_grid(self, surface: MathematicalSurface) -> List[Tuple[float, float, float]]:
        """Genera una griglia di punti sulla superficie."""
        points = []

        if surface.surface_type == 'plane':
            points = self._generate_plane_grid(surface)
        elif surface.surface_type == 'sphere':
            points = self._generate_sphere_grid(surface)
        elif surface.surface_type == 'cylinder':
            points = self._generate_cylinder_grid(surface)
        else:
            # Superficie custom - usa bounding box
            points = self._generate_bbox_grid(surface)

        return points

    def _generate_plane_grid(self, surface: MathematicalSurface) -> List[Tuple[float, float, float]]:
        """Genera griglia su superficie piana."""
        center = surface.parameters.get('center', (0, 0, 0))
        size = surface.parameters.get('size', (10, 10))

        points = []
        for i in range(self.resolution):
            for j in range(self.resolution):
                u = (i / (self.resolution - 1)) - 0.5  # -0.5 to 0.5
                v = (j / (self.resolution - 1)) - 0.5

                x = center[0] + u * size[0]
                y = center[1] + v * size[1]
                z = center[2]

                points.append((x, y, z))

        return points

    def _generate_sphere_grid(self, surface: MathematicalSurface) -> List[Tuple[float, float, float]]:
        """Genera griglia su superficie sferica."""
        center = surface.parameters.get('center', (0, 0, 0))
        radius = surface.parameters.get('radius', 10)
        z_min = surface.parameters.get('z_min', center[2] - radius)
        z_max = surface.parameters.get('z_max', center[2] + radius)

        points = []
        for i in range(self.resolution):
            for j in range(self.resolution):
                # Coordinate sferiche
                theta = (i / (self.resolution - 1)) * 2 * math.pi  # 0 to 2π
                phi_min = math.acos((z_max - center[2]) / radius) if z_max < center[2] + radius else 0
                phi_max = math.acos((z_min - center[2]) / radius) if z_min > center[2] - radius else math.pi
                phi = phi_min + (j / (self.resolution - 1)) * (phi_max - phi_min)

                # Conversione a coordinate cartesiane
                x = center[0] + radius * math.sin(phi) * math.cos(theta)
                y = center[1] + radius * math.sin(phi) * math.sin(theta)
                z = center[2] + radius * math.cos(phi)

                # Verifica che il punto sia nel range z specificato
                if z_min <= z <= z_max:
                    points.append((x, y, z))

        return points

    def _generate_cylinder_grid(self, surface: MathematicalSurface) -> List[Tuple[float, float, float]]:
        """Genera griglia su superficie cilindrica."""
        center = surface.parameters.get('center', (0, 0, 0))
        radius = surface.parameters.get('radius', 10)
        height = surface.parameters.get('height', 20)

        points = []
        for i in range(self.resolution):
            for j in range(self.resolution):
                theta = (i / (self.resolution - 1)) * 2 * math.pi
                z = center[2] + (j / (self.resolution - 1)) * height

                x = center[0] + radius * math.cos(theta)
                y = center[1] + radius * math.sin(theta)

                points.append((x, y, z))

        return points

    def _generate_bbox_grid(self, surface: MathematicalSurface) -> List[Tuple[float, float, float]]:
        """Genera griglia basata su bounding box."""
        bbox = surface.get_bounding_box()
        min_point, max_point = bbox

        points = []
        for i in range(self.resolution):
            for j in range(self.resolution):
                u = i / (self.resolution - 1)
                v = j / (self.resolution - 1)

                x = min_point[0] + u * (max_point[0] - min_point[0])
                y = min_point[1] + v * (max_point[1] - min_point[1])
                z = (min_point[2] + max_point[2]) / 2  # Piano medio

                points.append((x, y, z))

        return points

    def _calculate_surface_normal(self, surface: MathematicalSurface,
                                 point: Tuple[float, float, float]) -> Tuple[float, float, float]:
        """Calcola la normale alla superficie in un punto."""
        if surface.surface_type == 'plane':
            return surface.parameters.get('normal', (0, 0, 1))

        elif surface.surface_type == 'sphere':
            center = surface.parameters.get('center', (0, 0, 0))
            # Normale = direzione dal centro al punto
            dx = point[0] - center[0]
            dy = point[1] - center[1]
            dz = point[2] - center[2]
            length = math.sqrt(dx*dx + dy*dy + dz*dz)
            if length > 0:
                return (dx/length, dy/length, dz/length)
            return (0, 0, 1)

        elif surface.surface_type == 'cylinder':
            center = surface.parameters.get('center', (0, 0, 0))
            # Normale radiale (solo componenti X,Y)
            dx = point[0] - center[0]
            dy = point[1] - center[1]
            length = math.sqrt(dx*dx + dy*dy)
            if length > 0:
                return (dx/length, dy/length, 0)
            return (1, 0, 0)

        else:
            return (0, 0, 1)  # Default

    def _check_point_accessibility(self,
                                  point: Tuple[float, float, float],
                                  normal: Tuple[float, float, float],
                                  target_geometry: GeometricShape,
                                  approach_direction: Tuple[float, float, float],
                                  tool_length: float,
                                  safety_distance: float,
                                  tool) -> AccessibilityPoint:
        """Verifica l'accessibilità di un punto."""

        # Calcola posizione dell'utensile
        tool_position = (
            point[0] + normal[0] * (tool_length + safety_distance),
            point[1] + normal[1] * (tool_length + safety_distance),
            point[2] + normal[2] * (tool_length + safety_distance)
        )

        # Verifica se il punto è sulla superficie del target
        distance_to_surface = 0.0
        if target_geometry.contains_point(point):
            status = AccessibilityStatus.ACCESSIBLE
        else:
            # Calcola distanza alla superficie più vicina
            distance_to_surface = self._calculate_distance_to_surface(point, target_geometry)
            if distance_to_surface > 1.0:  # Soglia di tolleranza
                status = AccessibilityStatus.OUT_OF_REACH
            else:
                status = AccessibilityStatus.PARTIAL

        # Calcola angolo dell'utensile
        tool_angle = self._calculate_tool_angle(normal, approach_direction)

        # Verifica collisioni (semplificata)
        if tool and hasattr(tool, 'diameter'):
            if self._check_tool_collision(point, normal, target_geometry, tool.diameter):
                status = AccessibilityStatus.COLLISION

        return AccessibilityPoint(point, normal, status, distance_to_surface, tool_angle)

    def _calculate_distance_to_surface(self, point: Tuple[float, float, float],
                                     geometry: GeometricShape) -> float:
        """Calcola la distanza minima di un punto alla superficie."""
        if geometry.shape_type == 'sphere':
            center = geometry.parameters.get('center', (0, 0, 0))
            radius = geometry.parameters.get('radius', 10)

            dx = point[0] - center[0]
            dy = point[1] - center[1]
            dz = point[2] - center[2]
            distance_to_center = math.sqrt(dx*dx + dy*dy + dz*dz)

            return abs(distance_to_center - radius)

        # Per altre geometrie, implementazione semplificata
        return 0.0

    def _calculate_tool_angle(self, normal: Tuple[float, float, float],
                            approach_direction: Tuple[float, float, float]) -> float:
        """Calcola l'angolo tra la normale e la direzione di approccio."""
        # Prodotto scalare per calcolare l'angolo
        dot_product = (normal[0] * approach_direction[0] +
                      normal[1] * approach_direction[1] +
                      normal[2] * approach_direction[2])

        # Clamp per evitare errori numerici
        dot_product = max(-1.0, min(1.0, dot_product))

        angle_rad = math.acos(abs(dot_product))
        return math.degrees(angle_rad)

    def _check_tool_collision(self, point: Tuple[float, float, float],
                            normal: Tuple[float, float, float],
                            geometry: GeometricShape,
                            tool_diameter: float) -> bool:
        """Verifica se l'utensile collide con la geometria."""
        # Implementazione semplificata - verifica se il raggio dell'utensile
        # interseca con la geometria
        tool_radius = tool_diameter / 2.0

        # Punti di test attorno al punto principale
        test_points = [
            (point[0] + tool_radius, point[1], point[2]),
            (point[0] - tool_radius, point[1], point[2]),
            (point[0], point[1] + tool_radius, point[2]),
            (point[0], point[1] - tool_radius, point[2])
        ]

        for test_point in test_points:
            if geometry.contains_point(test_point):
                return True

        return False

    def create_accessibility_mesh(self) -> Optional[Any]:
        """Crea una mesh colorata per visualizzare l'accessibilità."""
        if not PYVISTA_AVAILABLE or not self.points:
            return None

        print(f"Creazione mesh di accessibilità con {len(self.points)} punti")

        # Estrai coordinate e stati
        coordinates = np.array([p.position for p in self.points])
        statuses = [p.status for p in self.points]

        # Crea point cloud
        point_cloud = pv.PolyData(coordinates)

        # Assegna colori basati sullo stato
        colors = []
        for status in statuses:
            if status == AccessibilityStatus.ACCESSIBLE:
                colors.append([0, 255, 0])      # Verde
            elif status == AccessibilityStatus.NOT_ACCESSIBLE:
                colors.append([255, 0, 0])      # Rosso
            elif status == AccessibilityStatus.PARTIAL:
                colors.append([255, 255, 0])    # Giallo
            elif status == AccessibilityStatus.COLLISION:
                colors.append([255, 0, 255])    # Magenta
            else:  # OUT_OF_REACH
                colors.append([128, 128, 128])  # Grigio

        point_cloud['colors'] = np.array(colors)

        # Crea mesh triangolata se possibile
        try:
            # Riorganizza i punti in griglia
            n = int(math.sqrt(len(self.points)))
            if n * n == len(self.points):
                # Crea mesh strutturata
                mesh = self._create_structured_mesh(coordinates, colors, n)
                self.mesh_data = mesh
                return mesh
        except:
            pass

        # Fallback: usa point cloud
        self.mesh_data = point_cloud
        return point_cloud

    def _create_structured_mesh(self, coordinates: np.ndarray,
                              colors: List[List[int]], grid_size: int) -> Any:
        """Crea una mesh strutturata da una griglia di punti."""
        # Riorganizza coordinate in griglia
        grid_coords = coordinates.reshape((grid_size, grid_size, 3))
        grid_colors = np.array(colors).reshape((grid_size, grid_size, 3))

        # Crea mesh strutturata
        mesh = pv.StructuredGrid()
        mesh.points = coordinates
        mesh.dimensions = (grid_size, grid_size, 1)
        mesh['colors'] = np.array(colors)

        return mesh

    def get_accessibility_statistics(self) -> Dict[str, Any]:
        """Calcola statistiche sull'accessibilità."""
        if not self.points:
            return {}

        total_points = len(self.points)
        status_counts = {}

        for status in AccessibilityStatus:
            count = sum(1 for p in self.points if p.status == status)
            status_counts[status.value] = {
                'count': count,
                'percentage': (count / total_points) * 100
            }

        # Calcola angoli medi
        accessible_points = [p for p in self.points if p.status == AccessibilityStatus.ACCESSIBLE]
        avg_tool_angle = 0.0
        if accessible_points:
            avg_tool_angle = sum(p.tool_angle for p in accessible_points) / len(accessible_points)

        return {
            'total_points': total_points,
            'status_distribution': status_counts,
            'average_tool_angle': avg_tool_angle,
            'accessibility_ratio': status_counts.get('accessible', {}).get('percentage', 0.0)
        }


def visualize_accessibility(analyzer: AccessibilityAnalyzer,
                          target_geometry: GeometricShape,
                          operation: Operation,
                          show_target: bool = True,
                          show_tool_paths: bool = False) -> None:
    """
    Visualizza l'analisi di accessibilità con PyVista.

    Args:
        analyzer: Analizzatore con dati di accessibilità
        target_geometry: Geometria del pezzo target
        operation: Operazione analizzata
        show_target: Se mostrare la geometria target
        show_tool_paths: Se mostrare i percorsi utensile
    """
    if not PYVISTA_AVAILABLE:
        print("⚠️  PyVista non disponibile per la visualizzazione")
        return

    if not analyzer.points:
        print("⚠️  Nessun dato di accessibilità da visualizzare")
        return

    print("🎨 Creazione visualizzazione accessibilità...")

    # Crea plotter
    plotter = pv.Plotter(window_size=(1400, 900))
    plotter.set_background('white')

    # 1. Aggiungi mesh di accessibilità
    accessibility_mesh = analyzer.create_accessibility_mesh()
    if accessibility_mesh:
        plotter.add_mesh(
            accessibility_mesh,
            scalars='colors',
            rgb=True,
            point_size=8,
            render_points_as_spheres=True,
            label='Accessibilità'
        )

    # 2. Aggiungi geometria target se richiesta
    if show_target and target_geometry:
        target_mesh = _create_geometry_mesh(target_geometry)
        if target_mesh:
            plotter.add_mesh(
                target_mesh,
                color='lightblue',
                opacity=0.3,
                label='Pezzo Target'
            )

    # 3. Aggiungi informazioni
    stats = analyzer.get_accessibility_statistics()
    info_text = _create_info_text(operation, stats)
    plotter.add_text(info_text, position='upper_left', font_size=10)

    # 4. Aggiungi legenda colori
    legend_text = """LEGENDA ACCESSIBILITÀ:
🟢 Verde: Accessibile
🔴 Rosso: Non Accessibile
🟡 Giallo: Parzialmente Accessibile
🟣 Magenta: Collisione Utensile
⚫ Grigio: Fuori Portata"""

    plotter.add_text(legend_text, position='upper_right', font_size=9)

    # 5. Configura vista
    plotter.add_axes()
    plotter.show_grid()
    plotter.camera_position = 'iso'

    # 6. Mostra
    print("✅ Visualizzazione pronta")
    plotter.show()


def _create_geometry_mesh(geometry: GeometricShape) -> Optional[Any]:
    """Crea una mesh PyVista dalla geometria."""
    if not PYVISTA_AVAILABLE:
        return None

    if geometry.shape_type == 'sphere':
        center = geometry.parameters.get('center', (0, 0, 0))
        radius = geometry.parameters.get('radius', 10)
        return pv.Sphere(radius=radius, center=center, phi_resolution=50, theta_resolution=50)

    elif geometry.shape_type == 'cylinder':
        center = geometry.parameters.get('center', (0, 0, 0))
        radius = geometry.parameters.get('radius', 10)
        height = geometry.parameters.get('height', 20)

        cylinder = pv.Cylinder(
            center=center,
            direction=(0, 0, 1),
            radius=radius,
            height=height,
            resolution=50
        )
        return cylinder

    elif geometry.shape_type == 'box':
        corner = geometry.parameters.get('corner', (0, 0, 0))
        length = geometry.parameters.get('length', 10)
        width = geometry.parameters.get('width', 10)
        height = geometry.parameters.get('height', 10)

        bounds = [
            corner[0], corner[0] + length,
            corner[1], corner[1] + width,
            corner[2], corner[2] + height
        ]
        return pv.Box(bounds=bounds)

    return None


def _create_info_text(operation: Operation, stats: Dict[str, Any]) -> str:
    """Crea testo informativo per la visualizzazione."""
    tool_name = operation.tool.name if operation.tool else "N/A"
    tool_diameter = f"Ø{operation.tool.diameter}mm" if operation.tool and hasattr(operation.tool, 'diameter') else "N/A"

    text = f"""ANALISI ACCESSIBILITÀ
Operazione: {operation.name}
Utensile: {tool_name} {tool_diameter}
Superfici: {len(operation.surfaces)}

STATISTICHE:
Punti totali: {stats.get('total_points', 0)}
Accessibilità: {stats.get('accessibility_ratio', 0):.1f}%
Angolo medio: {stats.get('average_tool_angle', 0):.1f}°

DISTRIBUZIONE:"""

    status_dist = stats.get('status_distribution', {})
    for status, data in status_dist.items():
        if data['count'] > 0:
            text += f"\n{status.title()}: {data['count']} ({data['percentage']:.1f}%)"

    return text
